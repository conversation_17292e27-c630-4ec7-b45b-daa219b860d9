"""
Server configuration and setup utilities
"""
from fastapi import <PERSON>AP<PERSON>, HTTPException, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.responses import JSONResponse
from contextlib import asynccontextmanager
import os

from .config import config
from .database.registry import DatabaseRegistry
from .templates.manager import get_template_manager
from .logging import initialize_logging, get_logger
from .utils.middleware import (
    timing_middleware, error_handling_middleware, logging_middleware,
    database_middleware, environment_middleware, transaction_middleware
)


class ServerConfig:
    """Server configuration and setup"""

    def __init__(self):
        self.template_manager = get_template_manager()
        self.logger = None
        self._initialize_logging()

    def _initialize_logging(self):
        """Setup logging for server config (assumes logging is already initialized)"""
        try:
            # Configure uvicorn logging to use our formatters
            from .logging.manager import get_logging_manager
            logging_manager = get_logging_manager()
            logging_manager.configure_uvicorn_logging()

            self.logger = get_logger(__name__)
            self.logger.debug("🎨 Server logging configuration applied")
        except Exception as e:
            # Fallback to basic logging if initialization fails
            import logging
            logging.basicConfig(level=logging.INFO)
            self.logger = logging.getLogger(__name__)
            self.logger.error(f"❌ Failed to initialize advanced logging: {e}")

    def create_app(self) -> FastAPI:
        """Create FastAPI application with lifespan management"""
        
        @asynccontextmanager
        async def lifespan(app: FastAPI):
            # Startup
            import time
            startup_start = time.perf_counter()
            self.logger.info("🚀 Starting ERP server...")

            try:
                # Setup template directories and load all templates
                self.logger.debug("Setting up template directories and loading templates")
                await self._setup_template_directories()

                # Load all templates into system_templates during startup
                self.logger.debug("Loading all templates into system_templates")
                template_count = await self.template_manager.load_all_templates_async()
                self.logger.info(f"✅ Loaded {template_count} template files into system_templates")

                # Setup HTTP routes
                self.logger.debug("Setting up HTTP routes")
                await self._setup_http_routes(app)
                self.logger.debug("HTTP routes registered")

                startup_duration = time.perf_counter() - startup_start
                self.logger.info(f"✅ ERP server startup completed in {startup_duration:.3f}s")
                print()

            except Exception as e:
                startup_duration = time.perf_counter() - startup_start
                self.logger.error(f"❌ ERP server startup failed after {startup_duration:.3f}s: {e}")
                raise

            yield

            # Shutdown
            shutdown_start = time.perf_counter()
            self.logger.info("🛑 Shutting down ERP server...")

            # Capture memory usage before shutdown
            from .logging.monitoring import get_memory_usage, log_memory_freed
            from .cluster import cluster_manager
            
            before_memory = get_memory_usage()
            self.logger.info(
                f"📊 Memory usage before shutdown: "
                f"Process RSS: {before_memory.get('process_rss_mb', 0):.2f} MB, "
                f"System: {before_memory.get('system_used_mb', 0):.2f} MB "
                f"({before_memory.get('system_percent', 0):.1f}%)"
            )
            
            # Log cluster-specific shutdown information
            cluster_manager.log_cluster_shutdown_info()

            try:
                await DatabaseRegistry.close_all()
                
                # Capture memory usage after shutdown
                after_memory = get_memory_usage()
                
                # Log memory freed
                log_memory_freed(self.logger, before_memory, after_memory)
                
                shutdown_duration = time.perf_counter() - shutdown_start
                self.logger.info(f"✅ ERP server shutdown completed in {shutdown_duration:.3f}s")
            except Exception as e:
                # Still try to log memory even if shutdown failed
                try:
                    after_memory = get_memory_usage()
                    log_memory_freed(self.logger, before_memory, after_memory)
                except Exception:
                    pass  # Don't let memory logging errors mask the original error
                    
                shutdown_duration = time.perf_counter() - shutdown_start
                self.logger.error(f"❌ ERP server shutdown failed after {shutdown_duration:.3f}s: {e}")
        
        app = FastAPI(
            title="ERP System",
            description="Odoo-like ERP system with async support",
            version="1.0.0",
            lifespan=lifespan
        )
        
        # Add custom exception handler to preserve detailed error information
        @app.exception_handler(HTTPException)
        async def custom_http_exception_handler(request: Request, exc: HTTPException):
            """Custom handler to preserve detailed error information"""
            return JSONResponse(
                status_code=exc.status_code,
                content=exc.detail if isinstance(exc.detail, dict) else {"detail": exc.detail}
            )
        
        return app
    
    def setup_static_files(self, app: FastAPI):
        """Setup static files"""
        # Mount static files
        if os.path.exists("static"):
            app.mount("/static", StaticFiles(directory="static"), name="static")
    
    def setup_middleware(self, app: FastAPI):
        """Setup FastAPI middleware"""
        # CORS middleware
        app.add_middleware(
            CORSMiddleware,
            allow_origins=config.cors_origins,
            allow_credentials=True,
            allow_methods=["*"],
            allow_headers=["*"],
        )
        
        # Custom middleware (order matters - execution is in reverse order of registration)
        # Note: FastAPI middleware executes in reverse order of registration
        # Execution order: database -> environment -> transaction -> logging -> error_handling -> timing
        app.middleware("http")(timing_middleware)        # Executes last
        app.middleware("http")(error_handling_middleware)
        app.middleware("http")(logging_middleware)
        app.middleware("http")(transaction_middleware)   # Executes after environment, before logging
        app.middleware("http")(environment_middleware)   # Executes after database, before transaction
        app.middleware("http")(database_middleware)      # Executes first



    async def _setup_template_directories(self):
        """Setup template directories from default locations"""
        # Add default template directory
        if os.path.exists("templates"):
            self.template_manager.add_template_directory("templates")

        print(f"Template directories: {self.template_manager.template_dirs}")

    async def _setup_http_routes(self, app: FastAPI):
        """Setup HTTP routes"""
        from .http import setup_http_routes

        # Register ONLY global/system routes (from @route decorators)
        # NO database routes, NO addon routes during server startup
        setup_http_routes(app)
        self.logger.debug("System HTTP routes registered")

        # Database-specific routes will be registered lazily when databases are accessed
        self.logger.debug("Database routes will be registered lazily when needed")



    def get_addon_info(self):
        """Get information about loaded addons"""
        return {}